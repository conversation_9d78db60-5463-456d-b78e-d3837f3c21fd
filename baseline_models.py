#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基线模型定义 - 用于Phase 1的消融研究
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from self_attention import Normalize, NeighborAttention, PositionWiseFeedForward
from gat_layer_improved import MultiHeadGATLayer
from gps_enhanced_layer import EnhancedGatedFusion

class NonGraphBaseline(nn.Module):
    """
    非图基线模型 - 使用1D-CNN处理节点特征序列
    不使用任何图结构信息，建立性能下限
    """
    def __init__(self, node_features, hidden_dim=128, num_layers=4, dropout=0.2):
        super(NonGraphBaseline, self).__init__()
        
        # 输入投影
        self.input_proj = nn.Sequential(
            nn.Linear(node_features, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, hidden_dim)
        )
        
        # 1D卷积层
        self.conv_layers = nn.ModuleList([
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1)
            for _ in range(num_layers)
        ])
        
        self.norm_layers = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(num_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
        self.relu = nn.ReLU()
        
        # 输出层
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
    def forward(self, X, V, mask, adj=None, pe=None):
        """
        前向传播 - 忽略图结构信息
        Args:
            V: 节点特征 [B, L, node_features]
            mask: 节点掩码 [B, L]
            其他参数被忽略
        """
        # 输入投影
        h = self.input_proj(V)  # [B, L, hidden_dim]
        
        # 1D卷积处理
        h = h.transpose(1, 2)  # [B, hidden_dim, L] for conv1d
        
        for conv, norm in zip(self.conv_layers, self.norm_layers):
            residual = h
            h = conv(h)
            h = h.transpose(1, 2)  # [B, L, hidden_dim] for layer norm
            h = norm(h)
            h = self.relu(h)
            h = self.dropout(h)
            h = h.transpose(1, 2)  # [B, hidden_dim, L] back to conv format
            h = h + residual  # 残差连接
        
        h = h.transpose(1, 2)  # [B, L, hidden_dim]
        
        # 应用掩码
        if mask is not None:
            h = h * mask.unsqueeze(-1)
        
        # 输出投影
        logits = self.output_proj(h).squeeze(-1)  # [B, L]
        
        return logits

class GeometryOnlyBaseline(nn.Module):
    """
    纯几何基线模型 - 只使用几何注意力，不使用拓扑信息
    """
    def __init__(self, node_features, edge_features, hidden_dim=128, num_layers=4, k_neighbors=30, dropout=0.2):
        super(GeometryOnlyBaseline, self).__init__()
        
        # 边特征提取（几何信息）
        from edge_features import EdgeFeatures
        self.EdgeFeatures = EdgeFeatures(edge_features, top_k=k_neighbors, augment_eps=0.)
        
        # 节点和边嵌入
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, hidden_dim)
        )
        
        self.edge_embedding = nn.Linear(edge_features, hidden_dim)
        
        # 几何注意力层
        self.attention_layers = nn.ModuleList([
            NeighborAttention(hidden_dim, hidden_dim * 2, num_heads=4)
            for _ in range(num_layers)
        ])
        
        self.norm_layers = nn.ModuleList([
            Normalize(hidden_dim) for _ in range(num_layers)
        ])
        
        self.ffn_layers = nn.ModuleList([
            PositionWiseFeedForward(hidden_dim, hidden_dim * 4)
            for _ in range(num_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
        self.relu = nn.LeakyReLU()
        
        # 输出层
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
    def forward(self, X, V, mask, adj=None, pe=None):
        """只使用几何注意力的前向传播"""
        from self_attention import gather_nodes, cat_neighbors_nodes
        
        # 提取边特征（几何信息）
        E, E_idx = self.EdgeFeatures(X, mask)
        
        # 节点和边嵌入
        h_V = self.node_embedding(V)
        h_E = self.edge_embedding(E)
        
        # 构建注意力掩码
        mask_attend = gather_nodes(mask.unsqueeze(-1), E_idx).squeeze(-1)
        mask_attend = mask.unsqueeze(-1) * mask_attend
        
        # 多层几何注意力
        for attention, norm, ffn in zip(self.attention_layers, self.norm_layers, self.ffn_layers):
            # 几何注意力
            h_EV = cat_neighbors_nodes(h_V, h_E, E_idx)
            dh = attention(h_V, h_EV, mask_attend)
            h_V = norm(h_V + self.dropout(dh))
            h_V = self.relu(h_V)
            
            # FFN
            dh = ffn(h_V)
            h_V = norm(h_V + self.dropout(dh))
            
            # 应用掩码
            if mask is not None:
                h_V = h_V * mask.unsqueeze(-1)
        
        # 输出投影
        logits = self.output_proj(h_V).squeeze(-1)
        
        return logits

class TopologyOnlyBaseline(nn.Module):
    """
    纯拓扑基线模型 - 只使用GAT处理拓扑结构，不使用几何信息
    """
    def __init__(self, node_features, hidden_dim=128, num_layers=4, dropout=0.2):
        super(TopologyOnlyBaseline, self).__init__()
        
        # 节点嵌入
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, hidden_dim)
        )
        
        # GAT层
        self.gat_layers = nn.ModuleList([
            MultiHeadGATLayer(hidden_dim, hidden_dim, n_heads=4, dropout=dropout)
            for _ in range(num_layers)
        ])
        
        self.norm_layers = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(num_layers)
        ])
        
        self.ffn_layers = nn.ModuleList([
            PositionWiseFeedForward(hidden_dim, hidden_dim * 4)
            for _ in range(num_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
        self.relu = nn.LeakyReLU()
        
        # 输出层
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
    def forward(self, X, V, mask, adj, pe=None):
        """只使用拓扑GAT的前向传播"""
        # 节点嵌入
        h_V = self.node_embedding(V)
        
        # 多层GAT
        for gat, norm, ffn in zip(self.gat_layers, self.norm_layers, self.ffn_layers):
            # GAT更新
            dh = gat(h_V, adj, mask)
            h_V = norm(h_V + self.dropout(dh))
            h_V = self.relu(h_V)
            
            # FFN
            dh = ffn(h_V)
            h_V = norm(h_V + self.dropout(dh))
            
            # 应用掩码
            if mask is not None:
                h_V = h_V * mask.unsqueeze(-1)
        
        # 输出投影
        logits = self.output_proj(h_V).squeeze(-1)

        return logits


# 模型工厂函数
def get_baseline_model(model_type, config):
    """
    根据类型创建基线模型
    """
    if model_type == 'non_graph':
        return NonGraphBaseline(
            node_features=config['node_features'],
            hidden_dim=config['hidden_dim'],
            num_layers=config['num_encoder_layers'],
            dropout=config['dropout']
        )
    elif model_type == 'geometry_only':
        return GeometryOnlyBaseline(
            node_features=config['node_features'],
            edge_features=config['edge_features'],
            hidden_dim=config['hidden_dim'],
            num_layers=config['num_encoder_layers'],
            k_neighbors=config['k_neighbors'],
            dropout=config['dropout']
        )
    elif model_type == 'topology_only':
        return TopologyOnlyBaseline(
            node_features=config['node_features'],
            hidden_dim=config['hidden_dim'],
            num_layers=config['num_encoder_layers'],
            dropout=config['dropout']
        )
    else:
        raise ValueError(f"Unknown baseline model type: {model_type}")
