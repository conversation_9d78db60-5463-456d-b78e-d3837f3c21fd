#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Phase 1: 简化基线测试
使用虚拟数据验证三个基线模型的相对性能
"""

import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.metrics import roc_auc_score
import warnings

from baseline_models import get_baseline_model
from loss import FocalLoss

warnings.simplefilter('ignore')

def create_synthetic_data(num_samples=100, seq_len=100):
    """创建合成数据用于测试"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 节点特征 (T5 + DSSP + BLOSUM62)
    node_features = 1024 + 22 + 20
    
    X = torch.randn(num_samples, seq_len, 3).to(device)  # 坐标
    V = torch.randn(num_samples, seq_len, node_features).to(device)  # 节点特征
    mask = torch.ones(num_samples, seq_len).to(device)  # 掩码
    
    # 创建稀疏邻接矩阵
    adj = torch.rand(num_samples, seq_len, seq_len).to(device)
    adj = (adj > 0.9).float()  # 10%的连接
    # 确保对称性
    adj = (adj + adj.transpose(-1, -2)) / 2
    adj = (adj > 0.5).float()
    
    pe = torch.randn(num_samples, seq_len, 32).to(device)  # 位置编码
    
    # 创建标签（模拟结合位点预测）
    # 让标签与某些特征相关，这样模型可以学习
    labels = torch.sigmoid(V[:, :, :10].sum(dim=-1) + torch.randn(num_samples, seq_len).to(device) * 0.1)
    labels = (labels > 0.5).float()
    
    return X, V, mask, adj, pe, labels

def evaluate_model_synthetic(model, X, V, mask, adj, pe, labels, criterion):
    """在合成数据上评估模型"""
    model.eval()
    
    with torch.no_grad():
        # 前向传播
        outputs = model(X, V, mask, adj, pe)
        
        # 计算损失
        loss = criterion(outputs, labels)
        
        # 计算AUC
        probs = torch.sigmoid(outputs)
        
        # 展平并移除填充
        flat_probs = []
        flat_labels = []
        
        for i in range(mask.shape[0]):
            valid_mask = mask[i].bool()
            if valid_mask.sum() > 0:
                flat_probs.append(probs[i][valid_mask].cpu().numpy())
                flat_labels.append(labels[i][valid_mask].cpu().numpy())
        
        if len(flat_probs) > 0:
            all_probs = np.concatenate(flat_probs)
            all_labels = np.concatenate(flat_labels)
            
            try:
                auc = roc_auc_score(all_labels, all_probs)
            except:
                auc = 0.5
        else:
            auc = 0.5
    
    return loss.item(), auc

def train_model_synthetic(model, X, V, mask, adj, pe, labels, epochs=20):
    """在合成数据上训练模型"""
    criterion = FocalLoss(alpha=0.25, gamma=2.0)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    model.train()
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(X, V, mask, adj, pe)
        
        # 计算损失
        loss = criterion(outputs, labels)
        
        # 反向传播
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        if epoch % 5 == 0:
            loss_val, auc = evaluate_model_synthetic(model, X, V, mask, adj, pe, labels, criterion)
            print(f"    Epoch {epoch:2d}: Loss = {loss_val:.4f}, AUC = {auc:.4f}")
    
    # 最终评估
    final_loss, final_auc = evaluate_model_synthetic(model, X, V, mask, adj, pe, labels, criterion)
    return final_auc

def run_phase1_synthetic():
    """运行Phase 1合成数据实验"""
    print("="*60)
    print("Phase 1: 基线实验 (合成数据)")
    print("="*60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 配置
    config = {
        'node_features': 1024 + 22 + 20,
        'edge_features': 32,
        'hidden_dim': 128,
        'num_encoder_layers': 2,
        'k_neighbors': 30,
        'dropout': 0.2,
    }
    
    # 创建合成数据
    print("创建合成数据...")
    X, V, mask, adj, pe, labels = create_synthetic_data(num_samples=50, seq_len=80)
    print(f"数据形状: X={X.shape}, V={V.shape}, labels={labels.shape}")
    
    # 基线模型类型
    baseline_types = ['non_graph', 'geometry_only', 'topology_only']
    results = {}
    
    # 运行每个基线
    for model_type in baseline_types:
        print(f"\n训练 {model_type} 基线模型...")
        
        try:
            # 创建模型
            model = get_baseline_model(model_type, config)
            model.to(device)
            
            print(f"  参数数量: {sum(p.numel() for p in model.parameters()):,}")
            
            # 训练模型
            final_auc = train_model_synthetic(model, X, V, mask, adj, pe, labels, epochs=15)
            
            results[model_type] = final_auc
            print(f"  ✅ {model_type} 完成，最终AUC: {final_auc:.4f}")
            
        except Exception as e:
            print(f"  ❌ {model_type} 失败: {str(e)}")
            import traceback
            traceback.print_exc()
            results[model_type] = 0.0
    
    # 输出结果
    print("\n" + "="*60)
    print("Phase 1 基线实验结果 (合成数据):")
    print("="*60)
    
    perf_baseline = results.get('non_graph', 0.0)
    perf_geom = results.get('geometry_only', 0.0)
    perf_topo = results.get('topology_only', 0.0)
    
    print(f"Perf_Baseline (非图):     {perf_baseline:.4f}")
    print(f"Perf_Geom (纯几何):       {perf_geom:.4f}")
    print(f"Perf_Topo (纯拓扑):       {perf_topo:.4f}")
    
    # 分析结果
    print("\n分析:")
    best_score = max(perf_baseline, perf_geom, perf_topo)
    
    if perf_geom == best_score:
        print("✨ 几何信息表现最佳，建议在Phase 2中以几何基线为起点")
        best_model = 'geometry_only'
    elif perf_topo == best_score:
        print("✨ 拓扑信息表现最佳，建议在Phase 2中以拓扑基线为起点")
        best_model = 'topology_only'
    else:
        print("⚠️  非图基线表现最佳，可能需要检查图结构信息的有效性")
        best_model = 'non_graph'
    
    print(f"性能提升: 几何 vs 非图 = {perf_geom - perf_baseline:+.4f}")
    print(f"性能提升: 拓扑 vs 非图 = {perf_topo - perf_baseline:+.4f}")
    
    return results, best_model

if __name__ == "__main__":
    results, best_model = run_phase1_synthetic()
    print(f"\n🎯 推荐的最佳基线: {best_model}")
    print("Phase 1 完成！可以继续进行 Phase 2 的受控迭代实验。")
