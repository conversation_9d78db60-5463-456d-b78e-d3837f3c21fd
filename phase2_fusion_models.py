#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Phase 2: 融合模型定义
从最佳基线出发，逐步加回复杂性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from self_attention import Normalize, NeighborAttention, PositionWiseFeedForward
from gat_layer_improved import MultiHeadGATLayer
from gps_enhanced_layer import EnhancedGatedFusion

class SimpleFusionModel(nn.Module):
    """
    简单融合模型 - 使用直接相加融合几何和拓扑信息
    """
    def __init__(self, node_features, edge_features, hidden_dim=128, num_layers=4, k_neighbors=30, dropout=0.2):
        super(SimpleFusionModel, self).__init__()
        
        # 边特征提取（几何信息）
        from edge_features import EdgeFeatures
        self.EdgeFeatures = EdgeFeatures(edge_features, top_k=k_neighbors, augment_eps=0.)
        
        # 节点和边嵌入
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, hidden_dim)
        )
        
        self.edge_embedding = nn.Linear(edge_features, hidden_dim)
        
        # 几何注意力层
        self.attention_layers = nn.ModuleList([
            NeighborAttention(hidden_dim, hidden_dim * 2, num_heads=4)
            for _ in range(num_layers)
        ])
        
        # GAT层（拓扑信息）
        self.gat_layers = nn.ModuleList([
            MultiHeadGATLayer(hidden_dim, hidden_dim, n_heads=4, dropout=dropout)
            for _ in range(num_layers)
        ])
        
        self.norm_layers = nn.ModuleList([
            Normalize(hidden_dim) for _ in range(num_layers * 2)  # 每层需要两个norm
        ])
        
        self.ffn_layers = nn.ModuleList([
            PositionWiseFeedForward(hidden_dim, hidden_dim * 4)
            for _ in range(num_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
        self.relu = nn.LeakyReLU()
        
        # 输出层
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
    def forward(self, X, V, mask, adj, pe=None):
        """简单融合的前向传播"""
        from self_attention import gather_nodes, cat_neighbors_nodes
        
        # 提取边特征（几何信息）
        E, E_idx = self.EdgeFeatures(X, mask)
        
        # 节点和边嵌入
        h_V = self.node_embedding(V)
        h_E = self.edge_embedding(E)
        
        # 构建注意力掩码
        mask_attend = gather_nodes(mask.unsqueeze(-1), E_idx).squeeze(-1)
        mask_attend = mask.unsqueeze(-1) * mask_attend
        
        # 多层处理
        for i, (attention, gat, ffn) in enumerate(zip(self.attention_layers, self.gat_layers, self.ffn_layers)):
            # 几何注意力更新
            h_EV = cat_neighbors_nodes(h_V, h_E, E_idx)
            dh_attn = attention(h_V, h_EV, mask_attend)
            
            # 拓扑GAT更新
            dh_gat = gat(h_V, adj, mask)
            
            # 简单相加融合
            dh_fused = dh_attn + dh_gat
            
            # 应用更新
            h_V = self.norm_layers[i*2](h_V + self.dropout(dh_fused))
            h_V = self.relu(h_V)
            
            # FFN
            dh = ffn(h_V)
            h_V = self.norm_layers[i*2+1](h_V + self.dropout(dh))
            
            # 应用掩码
            if mask is not None:
                h_V = h_V * mask.unsqueeze(-1)
        
        # 输出投影
        logits = self.output_proj(h_V).squeeze(-1)
        
        return logits

class GatedFusionModel(nn.Module):
    """
    门控融合模型 - 使用EnhancedGatedFusion融合几何和拓扑信息
    """
    def __init__(self, node_features, edge_features, hidden_dim=128, num_layers=4, k_neighbors=30, dropout=0.2):
        super(GatedFusionModel, self).__init__()
        
        # 边特征提取（几何信息）
        from edge_features import EdgeFeatures
        self.EdgeFeatures = EdgeFeatures(edge_features, top_k=k_neighbors, augment_eps=0.)
        
        # 节点和边嵌入
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, hidden_dim)
        )
        
        self.edge_embedding = nn.Linear(edge_features, hidden_dim)
        
        # 几何注意力层
        self.attention_layers = nn.ModuleList([
            NeighborAttention(hidden_dim, hidden_dim * 2, num_heads=4)
            for _ in range(num_layers)
        ])
        
        # GAT层（拓扑信息）
        self.gat_layers = nn.ModuleList([
            MultiHeadGATLayer(hidden_dim, hidden_dim, n_heads=4, dropout=dropout)
            for _ in range(num_layers)
        ])
        
        # 门控融合模块
        self.fusion_layers = nn.ModuleList([
            EnhancedGatedFusion(hidden_dim, dropout=dropout)
            for _ in range(num_layers)
        ])
        
        self.norm_layers = nn.ModuleList([
            Normalize(hidden_dim) for _ in range(num_layers * 2)
        ])
        
        self.ffn_layers = nn.ModuleList([
            PositionWiseFeedForward(hidden_dim, hidden_dim * 4)
            for _ in range(num_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
        self.relu = nn.LeakyReLU()
        
        # 输出层
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
    def forward(self, X, V, mask, adj, pe=None):
        """门控融合的前向传播"""
        from self_attention import gather_nodes, cat_neighbors_nodes
        
        # 提取边特征（几何信息）
        E, E_idx = self.EdgeFeatures(X, mask)
        
        # 节点和边嵌入
        h_V = self.node_embedding(V)
        h_E = self.edge_embedding(E)
        
        # 构建注意力掩码
        mask_attend = gather_nodes(mask.unsqueeze(-1), E_idx).squeeze(-1)
        mask_attend = mask.unsqueeze(-1) * mask_attend
        
        # 多层处理
        for i, (attention, gat, fusion, ffn) in enumerate(zip(
            self.attention_layers, self.gat_layers, self.fusion_layers, self.ffn_layers
        )):
            # 几何注意力更新
            h_EV = cat_neighbors_nodes(h_V, h_E, E_idx)
            dh_attn = attention(h_V, h_EV, mask_attend)
            
            # 拓扑GAT更新
            dh_gat = gat(h_V, adj, mask)
            
            # 门控融合
            h_V_fused = fusion(h_V, dh_attn, dh_gat)
            
            # 应用归一化
            h_V = self.norm_layers[i*2](h_V_fused)
            h_V = self.relu(h_V)
            
            # FFN
            dh = ffn(h_V)
            h_V = self.norm_layers[i*2+1](h_V + self.dropout(dh))
            
            # 应用掩码
            if mask is not None:
                h_V = h_V * mask.unsqueeze(-1)
        
        # 输出投影
        logits = self.output_proj(h_V).squeeze(-1)
        
        return logits

# 模型工厂函数
def get_fusion_model(model_type, config):
    """
    根据类型创建融合模型
    """
    if model_type == 'simple_fusion':
        return SimpleFusionModel(
            node_features=config['node_features'],
            edge_features=config['edge_features'],
            hidden_dim=config['hidden_dim'],
            num_layers=config['num_encoder_layers'],
            k_neighbors=config['k_neighbors'],
            dropout=config['dropout']
        )
    elif model_type == 'gated_fusion':
        return GatedFusionModel(
            node_features=config['node_features'],
            edge_features=config['edge_features'],
            hidden_dim=config['hidden_dim'],
            num_layers=config['num_encoder_layers'],
            k_neighbors=config['k_neighbors'],
            dropout=config['dropout']
        )
    else:
        raise ValueError(f"Unknown fusion model type: {model_type}")
