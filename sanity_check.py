#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
健全性检查脚本 - 验证清理后的代码能够正常运行
在单个小批量上进行过拟合测试
"""

import os
import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader
from model import MVGNN
from utils import TaskDataset
from loss import FocalLoss
import warnings

warnings.simplefilter('ignore')

def sanity_check():
    """
    在单个小批量上进行过拟合测试，验证模型核心逻辑
    """
    print("=" * 60)
    print("开始健全性检查：单批量过拟合测试")
    print("=" * 60)
    
    # 检查CUDA可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 模型配置
    config = {
        'node_features': 1024 + 22 + 20,  # T5 + DSSP + BLOSUM62
        'edge_features': 32,
        'hidden_dim': 128,
        'num_encoder_layers': 2,  # 减少层数加快测试
        'k_neighbors': 30,
        'augment_eps': 0.0,
        'dropout': 0.1,
        'batch_size': 2,  # 小批量
    }
    
    # 创建模型
    print("初始化模型...")
    model = MVGNN(
        node_features=config['node_features'],
        edge_features=config['edge_features'], 
        hidden_dim=config['hidden_dim'],
        num_encoder_layers=config['num_encoder_layers'],
        k_neighbors=config['k_neighbors'],
        augment_eps=config['augment_eps'],
        dropout=config['dropout']
    )
    model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建虚拟数据进行测试
    print("创建测试数据...")
    batch_size = config['batch_size']
    seq_len = 100  # 较短的序列长度
    
    # 虚拟输入数据
    X = torch.randn(batch_size, seq_len, 3).to(device)  # 坐标
    V = torch.randn(batch_size, seq_len, config['node_features']).to(device)  # 节点特征
    mask = torch.ones(batch_size, seq_len).to(device)  # 掩码
    adj = torch.rand(batch_size, seq_len, seq_len).to(device)  # 邻接矩阵
    adj = (adj > 0.8).float()  # 稀疏化
    pe = torch.randn(batch_size, seq_len, 32).to(device)  # 位置编码
    labels = torch.randint(0, 2, (batch_size, seq_len)).float().to(device)  # 标签
    
    # 损失函数和优化器
    criterion = FocalLoss(alpha=0.25, gamma=2.0)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    print("开始过拟合测试...")
    model.train()
    
    initial_loss = None
    for epoch in range(50):  # 50个epoch应该足够过拟合
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(X, V, mask, adj, pe)
        
        # 计算损失
        loss = criterion(outputs, labels)
        
        if initial_loss is None:
            initial_loss = loss.item()
        
        # 反向传播
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch:2d}: Loss = {loss.item():.6f}")
    
    final_loss = loss.item()
    
    print("\n" + "=" * 60)
    print("健全性检查结果:")
    print(f"初始损失: {initial_loss:.6f}")
    print(f"最终损失: {final_loss:.6f}")
    print(f"损失下降: {initial_loss - final_loss:.6f}")
    print(f"损失下降比例: {(initial_loss - final_loss) / initial_loss * 100:.2f}%")
    
    # 判断是否通过测试
    if final_loss < initial_loss * 0.1:  # 损失下降到初始值的10%以下
        print("✅ 健全性检查通过！模型能够正常过拟合小批量数据")
        return True
    else:
        print("❌ 健全性检查失败！模型无法有效过拟合，可能存在问题")
        return False

if __name__ == "__main__":
    success = sanity_check()
    exit(0 if success else 1)
