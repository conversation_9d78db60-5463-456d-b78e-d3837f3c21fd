#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Phase 2: 受控迭代与消融研究
从最佳基线出发，逐步加回复杂性，验证每个模块的价值
"""

import torch
import torch.nn as nn
import numpy as np
from sklearn.metrics import roc_auc_score
import warnings
import json
from datetime import datetime

from baseline_models import get_baseline_model
from phase2_fusion_models import get_fusion_model
from model import MVGNN
from loss import FocalLoss

warnings.simplefilter('ignore')

def create_enhanced_synthetic_data(num_samples=100, seq_len=100):
    """
    创建增强的合成数据，让几何和拓扑信息更有意义
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 节点特征 (T5 + DSSP + BLOSUM62)
    node_features = 1024 + 22 + 20
    
    X = torch.randn(num_samples, seq_len, 3).to(device)  # 坐标
    V = torch.randn(num_samples, seq_len, node_features).to(device)  # 节点特征
    mask = torch.ones(num_samples, seq_len).to(device)  # 掩码
    
    # 创建更有意义的邻接矩阵（基于距离）
    adj = torch.zeros(num_samples, seq_len, seq_len).to(device)
    for i in range(num_samples):
        # 计算距离矩阵
        coords = X[i]  # [seq_len, 3]
        dist_matrix = torch.cdist(coords, coords)  # [seq_len, seq_len]
        
        # 基于距离创建邻接矩阵（距离小于阈值的节点相连）
        threshold = torch.quantile(dist_matrix, 0.2)  # 20%最近的邻居
        adj[i] = (dist_matrix < threshold).float()
        
        # 移除自连接
        adj[i].fill_diagonal_(0)
    
    pe = torch.randn(num_samples, seq_len, 32).to(device)  # 位置编码
    
    # 创建更复杂的标签，同时依赖节点特征、几何和拓扑信息
    labels = torch.zeros(num_samples, seq_len).to(device)
    
    for i in range(num_samples):
        # 1. 基于节点特征的贡献
        node_contrib = torch.sigmoid(V[i, :, :10].sum(dim=-1))
        
        # 2. 基于几何的贡献（坐标的某种组合）
        geom_contrib = torch.sigmoid(X[i, :, 0] + X[i, :, 1] * X[i, :, 2])
        
        # 3. 基于拓扑的贡献（邻居数量）
        topo_contrib = torch.sigmoid(adj[i].sum(dim=-1) / 10.0)
        
        # 综合贡献
        combined = 0.4 * node_contrib + 0.3 * geom_contrib + 0.3 * topo_contrib
        labels[i] = (combined > 0.5).float()
    
    return X, V, mask, adj, pe, labels

def evaluate_model_enhanced(model, X, V, mask, adj, pe, labels, criterion):
    """在增强合成数据上评估模型"""
    model.eval()
    
    with torch.no_grad():
        # 前向传播
        outputs = model(X, V, mask, adj, pe)
        
        # 计算损失
        loss = criterion(outputs, labels)
        
        # 计算AUC
        probs = torch.sigmoid(outputs)
        
        # 展平并移除填充
        flat_probs = []
        flat_labels = []
        
        for i in range(mask.shape[0]):
            valid_mask = mask[i].bool()
            if valid_mask.sum() > 0:
                flat_probs.append(probs[i][valid_mask].cpu().numpy())
                flat_labels.append(labels[i][valid_mask].cpu().numpy())
        
        if len(flat_probs) > 0:
            all_probs = np.concatenate(flat_probs)
            all_labels = np.concatenate(flat_labels)
            
            try:
                auc = roc_auc_score(all_labels, all_probs)
            except:
                auc = 0.5
        else:
            auc = 0.5
    
    return loss.item(), auc

def train_model_enhanced(model, X, V, mask, adj, pe, labels, epochs=20):
    """在增强合成数据上训练模型"""
    criterion = FocalLoss(alpha=0.25, gamma=2.0)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    model.train()
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(X, V, mask, adj, pe)
        
        # 计算损失
        loss = criterion(outputs, labels)
        
        # 反向传播
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        if epoch % 5 == 0:
            loss_val, auc = evaluate_model_enhanced(model, X, V, mask, adj, pe, labels, criterion)
            print(f"    Epoch {epoch:2d}: Loss = {loss_val:.4f}, AUC = {auc:.4f}")
    
    # 最终评估
    final_loss, final_auc = evaluate_model_enhanced(model, X, V, mask, adj, pe, labels, criterion)
    return final_auc

def run_phase2_experiment():
    """运行Phase 2受控迭代实验"""
    print("="*60)
    print("Phase 2: 受控迭代与消融研究")
    print("="*60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 配置
    config = {
        'node_features': 1024 + 22 + 20,
        'edge_features': 32,
        'hidden_dim': 128,
        'num_encoder_layers': 2,
        'k_neighbors': 30,
        'dropout': 0.2,
    }
    
    # 创建增强的合成数据
    print("创建增强合成数据（几何和拓扑信息更有意义）...")
    X, V, mask, adj, pe, labels = create_enhanced_synthetic_data(num_samples=50, seq_len=80)
    print(f"数据形状: X={X.shape}, V={V.shape}, labels={labels.shape}")
    
    # 实验模型类型（按照计划的顺序）
    model_types = [
        ('non_graph', 'baseline'),
        ('geometry_only', 'baseline'),
        ('topology_only', 'baseline'),
        ('simple_fusion', 'fusion'),
        ('gated_fusion', 'fusion'),
    ]
    
    results = {}
    
    # 运行每个模型
    for model_type, model_category in model_types:
        print(f"\n训练 {model_type} 模型...")
        
        try:
            # 创建模型
            if model_category == 'baseline':
                model = get_baseline_model(model_type, config)
            else:  # fusion
                model = get_fusion_model(model_type, config)
            
            model.to(device)
            
            print(f"  参数数量: {sum(p.numel() for p in model.parameters()):,}")
            
            # 训练模型
            final_auc = train_model_enhanced(model, X, V, mask, adj, pe, labels, epochs=15)
            
            results[model_type] = final_auc
            print(f"  ✅ {model_type} 完成，最终AUC: {final_auc:.4f}")
            
        except Exception as e:
            print(f"  ❌ {model_type} 失败: {str(e)}")
            import traceback
            traceback.print_exc()
            results[model_type] = 0.0
    
    # 输出结果
    print("\n" + "="*60)
    print("Phase 2 受控迭代实验结果:")
    print("="*60)
    
    perf_baseline = results.get('non_graph', 0.0)
    perf_geom = results.get('geometry_only', 0.0)
    perf_topo = results.get('topology_only', 0.0)
    perf_simple_fusion = results.get('simple_fusion', 0.0)
    perf_gated_fusion = results.get('gated_fusion', 0.0)
    
    print(f"Perf_Baseline (非图):         {perf_baseline:.4f}")
    print(f"Perf_Geom (纯几何):           {perf_geom:.4f}")
    print(f"Perf_Topo (纯拓扑):           {perf_topo:.4f}")
    print(f"Perf_SimpleFusion (简单融合): {perf_simple_fusion:.4f}")
    print(f"Perf_GatedFusion (门控融合):  {perf_gated_fusion:.4f}")
    
    # 分析结果
    print("\n分析:")
    print(f"几何 vs 非图:     {perf_geom - perf_baseline:+.4f}")
    print(f"拓扑 vs 非图:     {perf_topo - perf_baseline:+.4f}")
    print(f"简单融合 vs 最佳单视图: {perf_simple_fusion - max(perf_geom, perf_topo):+.4f}")
    print(f"门控融合 vs 简单融合:   {perf_gated_fusion - perf_simple_fusion:+.4f}")
    
    # 确定最佳模型
    best_score = max(results.values())
    best_model = max(results.keys(), key=lambda k: results[k])
    
    print(f"\n🎯 最佳模型: {best_model} (AUC: {best_score:.4f})")
    
    # 保存结果
    result_data = {
        'timestamp': datetime.now().isoformat(),
        'config': config,
        'results': results,
        'analysis': {
            'best_model': best_model,
            'best_score': best_score,
            'improvements': {
                'geometry_vs_baseline': perf_geom - perf_baseline,
                'topology_vs_baseline': perf_topo - perf_baseline,
                'simple_fusion_vs_best_single': perf_simple_fusion - max(perf_geom, perf_topo),
                'gated_fusion_vs_simple': perf_gated_fusion - perf_simple_fusion
            }
        }
    }
    
    with open('phase2_results.json', 'w') as f:
        json.dump(result_data, f, indent=2)
    
    print(f"\n结果已保存到 phase2_results.json")
    
    return results, best_model

if __name__ == "__main__":
    results, best_model = run_phase2_experiment()
    print(f"\nPhase 2 完成！推荐进入 Phase 3 使用 {best_model} 进行超参数优化。")
