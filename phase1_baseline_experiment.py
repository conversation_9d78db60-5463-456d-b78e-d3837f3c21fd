#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Phase 1: 基线实验 - 使用真实数据
建立三个关键基线：非图、纯几何、纯拓扑
"""

import os
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader
from sklearn.model_selection import KFold
from sklearn.metrics import roc_auc_score, average_precision_score
from tqdm import tqdm
import warnings
import json
from datetime import datetime

from baseline_models import get_baseline_model
from model import MVGNN
from utils import TaskDataset, Seed_everything
from loss import FocalLoss

warnings.simplefilter('ignore')

def quick_evaluate(model, dataloader, criterion, device, max_batches=10):
    """快速评估模型性能（只用前几个batch）"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for i, data in enumerate(dataloader):
            if i >= max_batches:
                break
                
            if len(data) == 6:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = data
                pe = None
            else:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj, pe = data
            
            # 移动到设备
            protein_X = protein_X.to(device)
            protein_node_features = protein_node_features.to(device)
            protein_masks = protein_masks.to(device)
            labels = labels.to(device)
            adj = adj.to(device)
            if pe is not None:
                pe = pe.to(device)
            
            # 前向传播
            try:
                if pe is not None:
                    outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                else:
                    outputs = model(protein_X, protein_node_features, protein_masks, adj)
                
                # 计算损失
                loss = 0
                for j in range(len(protein_masks)):
                    mask = protein_masks[j].bool()
                    if mask.sum() > 0:
                        loss += criterion(outputs[j][mask], labels[j][mask])
                loss = loss / len(protein_masks)
                total_loss += loss.item()
                
                # 收集预测和标签
                probs = torch.sigmoid(outputs)
                for j in range(len(protein_masks)):
                    mask = protein_masks[j].bool()
                    if mask.sum() > 0:
                        all_preds.append(probs[j][mask].cpu().numpy())
                        all_labels.append(labels[j][mask].cpu().numpy())
            except Exception as e:
                print(f"评估时出错: {str(e)}")
                continue
    
    if len(all_preds) == 0:
        return 0.5, 0.5, float('inf')
    
    # 计算指标
    all_preds = np.concatenate(all_preds)
    all_labels = np.concatenate(all_labels)
    
    try:
        auc = roc_auc_score(all_labels, all_preds)
        ap = average_precision_score(all_labels, all_preds)
    except:
        auc = 0.5
        ap = 0.5
    
    avg_loss = total_loss / min(len(dataloader), max_batches)
    
    return auc, ap, avg_loss

def train_baseline_quick(model_type, train_df, val_df, config, device):
    """快速训练基线模型（用于概念验证）"""
    print(f"\n训练 {model_type} 基线模型...")
    
    # 创建模型
    model = get_baseline_model(model_type, config)
    model.to(device)
    
    print(f"  参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建数据集（使用较小的子集）
    train_subset = train_df.head(20)  # 只用前20个样本
    val_subset = val_df.head(10)      # 只用前10个样本

    # 创建虚拟的protein_data字典
    protein_data = {}

    train_dataset = TaskDataset(train_subset, protein_data, 'label')
    val_dataset = TaskDataset(val_subset, protein_data, 'label')
    
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=2,  # 小批量
        collate_fn=train_dataset.collate_fn,
        shuffle=True,
        drop_last=True
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=2,
        collate_fn=val_dataset.collate_fn,
        shuffle=False,
        drop_last=False
    )
    
    # 损失函数和优化器
    criterion = FocalLoss(alpha=0.25, gamma=2.0)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    
    best_auc = 0
    epochs = 5  # 只训练5个epoch
    
    for epoch in range(epochs):
        # 训练
        model.train()
        train_loss = 0
        train_batches = 0
        
        for data in train_dataloader:
            if train_batches >= 5:  # 只训练5个batch
                break
                
            if len(data) == 6:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = data
                pe = None
            else:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj, pe = data
            
            # 移动到设备
            protein_X = protein_X.to(device)
            protein_node_features = protein_node_features.to(device)
            protein_masks = protein_masks.to(device)
            labels = labels.to(device)
            adj = adj.to(device)
            if pe is not None:
                pe = pe.to(device)
            
            optimizer.zero_grad()
            
            try:
                # 前向传播
                if pe is not None:
                    outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                else:
                    outputs = model(protein_X, protein_node_features, protein_masks, adj)
                
                # 计算损失
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        loss += criterion(outputs[i][mask], labels[i][mask])
                loss = loss / len(protein_masks)
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                train_batches += 1
                
            except Exception as e:
                print(f"  训练时出错: {str(e)}")
                continue
        
        # 验证
        val_auc, val_ap, val_loss = quick_evaluate(model, val_dataloader, criterion, device, max_batches=3)
        
        avg_train_loss = train_loss / max(train_batches, 1)
        print(f"  Epoch {epoch+1}: Train Loss = {avg_train_loss:.4f}, Val AUC = {val_auc:.4f}")
        
        if val_auc > best_auc:
            best_auc = val_auc
    
    return best_auc

def run_phase1_experiment():
    """运行Phase 1基线实验"""
    print("="*60)
    print("Phase 1: 基线实验开始")
    print("="*60)
    
    # 设置随机种子
    Seed_everything(42)
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 配置
    config = {
        'node_features': 1024 + 22 + 20,  # T5 + DSSP + BLOSUM62
        'edge_features': 32,
        'hidden_dim': 128,
        'num_encoder_layers': 2,  # 减少层数
        'k_neighbors': 30,
        'dropout': 0.2,
    }
    
    # 加载蛋白质ID列表
    print("加载蛋白质数据...")
    with open('protein_id_list.txt', 'r') as f:
        protein_ids = [line.strip() for line in f.readlines()]
    
    print(f"总共有 {len(protein_ids)} 个蛋白质")
    
    # 创建数据框
    df = pd.DataFrame({'pdb_id': protein_ids})
    
    # 简单的训练/验证分割
    split_idx = int(len(df) * 0.8)
    train_df = df[:split_idx].reset_index(drop=True)
    val_df = df[split_idx:].reset_index(drop=True)
    
    print(f"训练集: {len(train_df)} 个蛋白质")
    print(f"验证集: {len(val_df)} 个蛋白质")
    
    # 基线模型类型
    baseline_types = ['non_graph', 'geometry_only', 'topology_only']
    results = {}
    
    # 运行每个基线
    for model_type in baseline_types:
        try:
            auc = train_baseline_quick(model_type, train_df, val_df, config, device)
            results[model_type] = auc
            print(f"  ✅ {model_type} 完成，AUC: {auc:.4f}")
        except Exception as e:
            print(f"  ❌ {model_type} 失败: {str(e)}")
            results[model_type] = 0.0
    
    # 输出结果
    print("\n" + "="*60)
    print("Phase 1 基线实验结果:")
    print("="*60)
    
    perf_baseline = results.get('non_graph', 0.0)
    perf_geom = results.get('geometry_only', 0.0)
    perf_topo = results.get('topology_only', 0.0)
    
    print(f"Perf_Baseline (非图):     {perf_baseline:.4f}")
    print(f"Perf_Geom (纯几何):       {perf_geom:.4f}")
    print(f"Perf_Topo (纯拓扑):       {perf_topo:.4f}")
    
    # 分析结果
    print("\n分析:")
    best_baseline = max(perf_baseline, perf_geom, perf_topo)
    if perf_geom == best_baseline:
        print("✨ 几何信息表现最佳，建议在Phase 2中以几何基线为起点")
    elif perf_topo == best_baseline:
        print("✨ 拓扑信息表现最佳，建议在Phase 2中以拓扑基线为起点")
    else:
        print("⚠️  非图基线表现最佳，可能需要检查图结构信息的有效性")
    
    # 保存结果
    result_data = {
        'timestamp': datetime.now().isoformat(),
        'config': config,
        'results': results,
        'analysis': {
            'best_baseline': best_baseline,
            'best_model': max(results.keys(), key=lambda k: results[k])
        }
    }
    
    with open('phase1_results.json', 'w') as f:
        json.dump(result_data, f, indent=2)
    
    print(f"\n结果已保存到 phase1_results.json")
    
    return results

if __name__ == "__main__":
    results = run_phase1_experiment()
