#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试基线模型是否能正常创建和运行
"""

import torch
import warnings
from baseline_models import get_baseline_model

warnings.simplefilter('ignore')

def test_baseline_models():
    """测试所有基线模型"""
    print("测试基线模型...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 配置
    config = {
        'node_features': 1024 + 22 + 20,
        'edge_features': 32,
        'hidden_dim': 128,
        'num_encoder_layers': 2,
        'k_neighbors': 30,
        'dropout': 0.2,
    }
    
    # 测试数据
    batch_size = 2
    seq_len = 50
    
    X = torch.randn(batch_size, seq_len, 3).to(device)
    V = torch.randn(batch_size, seq_len, config['node_features']).to(device)
    mask = torch.ones(batch_size, seq_len).to(device)
    adj = torch.rand(batch_size, seq_len, seq_len).to(device)
    adj = (adj > 0.8).float()
    pe = torch.randn(batch_size, seq_len, 32).to(device)
    
    baseline_types = ['non_graph', 'geometry_only', 'topology_only']
    
    for model_type in baseline_types:
        print(f"\n测试 {model_type} 模型...")
        
        try:
            # 创建模型
            model = get_baseline_model(model_type, config)
            model.to(device)
            model.eval()
            
            print(f"  参数数量: {sum(p.numel() for p in model.parameters()):,}")
            
            # 前向传播测试
            with torch.no_grad():
                if model_type == 'non_graph':
                    outputs = model(X, V, mask)
                elif model_type == 'geometry_only':
                    outputs = model(X, V, mask, adj, pe)
                else:  # topology_only
                    outputs = model(X, V, mask, adj, pe)
            
            print(f"  输出形状: {outputs.shape}")
            print(f"  输出范围: [{outputs.min().item():.3f}, {outputs.max().item():.3f}]")
            print(f"  ✅ {model_type} 模型测试通过")
            
        except Exception as e:
            print(f"  ❌ {model_type} 模型测试失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_baseline_models()
