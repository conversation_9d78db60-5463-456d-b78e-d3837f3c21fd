#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Phase 1: 基线实验脚本
运行三个基线模型：非图、纯几何、纯拓扑
"""

import os
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader
from sklearn.model_selection import KFold
from sklearn.metrics import roc_auc_score, average_precision_score
from tqdm import tqdm
import warnings

from baseline_models import get_baseline_model
from model import MVGNN
from utils import TaskDataset, Seed_everything
from loss import FocalLoss

warnings.simplefilter('ignore')

def evaluate_model(model, dataloader, criterion, device):
    """评估模型性能"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for data in tqdm(dataloader, desc="Evaluating"):
            if len(data) == 6:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = data
                pe = None
            else:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj, pe = data
            
            # 移动到设备
            protein_X = protein_X.to(device)
            protein_node_features = protein_node_features.to(device)
            protein_masks = protein_masks.to(device)
            labels = labels.to(device)
            adj = adj.to(device)
            if pe is not None:
                pe = pe.to(device)
            
            # 前向传播
            if pe is not None:
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
            else:
                outputs = model(protein_X, protein_node_features, protein_masks, adj)
            
            # 计算损失
            loss = 0
            for i in range(len(protein_masks)):
                mask = protein_masks[i].bool()
                if mask.sum() > 0:
                    loss += criterion(outputs[i][mask], labels[i][mask])
            loss = loss / len(protein_masks)
            total_loss += loss.item()
            
            # 收集预测和标签
            probs = torch.sigmoid(outputs)
            for i in range(len(protein_masks)):
                mask = protein_masks[i].bool()
                if mask.sum() > 0:
                    all_preds.append(probs[i][mask].cpu().numpy())
                    all_labels.append(labels[i][mask].cpu().numpy())
    
    # 计算指标
    all_preds = np.concatenate(all_preds)
    all_labels = np.concatenate(all_labels)
    
    auc = roc_auc_score(all_labels, all_preds)
    ap = average_precision_score(all_labels, all_preds)
    avg_loss = total_loss / len(dataloader)
    
    return auc, ap, avg_loss

def train_baseline_model(model_type, train_df, val_df, config, fold, device):
    """训练单个基线模型"""
    print(f"\n{'='*60}")
    print(f"训练 {model_type} 基线模型 - Fold {fold}")
    print(f"{'='*60}")
    
    # 创建模型
    model = get_baseline_model(model_type, config)
    model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建数据集
    train_dataset = TaskDataset(train_df, mode='train')
    val_dataset = TaskDataset(val_df, mode='val')
    
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        collate_fn=train_dataset.collate_fn,
        shuffle=True,
        drop_last=True
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        collate_fn=val_dataset.collate_fn,
        shuffle=False,
        drop_last=False
    )
    
    # 损失函数和优化器
    criterion = FocalLoss(alpha=0.25, gamma=2.0)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=3, verbose=True
    )
    
    best_auc = 0
    patience_counter = 0
    max_patience = 10
    
    for epoch in range(config['epochs']):
        # 训练
        model.train()
        train_loss = 0
        
        for data in tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{config['epochs']}"):
            if len(data) == 6:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = data
                pe = None
            else:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj, pe = data
            
            # 移动到设备
            protein_X = protein_X.to(device)
            protein_node_features = protein_node_features.to(device)
            protein_masks = protein_masks.to(device)
            labels = labels.to(device)
            adj = adj.to(device)
            if pe is not None:
                pe = pe.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            if pe is not None:
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
            else:
                outputs = model(protein_X, protein_node_features, protein_masks, adj)
            
            # 计算损失
            loss = 0
            for i in range(len(protein_masks)):
                mask = protein_masks[i].bool()
                if mask.sum() > 0:
                    loss += criterion(outputs[i][mask], labels[i][mask])
            loss = loss / len(protein_masks)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
        
        # 验证
        val_auc, val_ap, val_loss = evaluate_model(model, val_dataloader, criterion, device)
        scheduler.step(val_auc)
        
        print(f"Epoch {epoch+1}: Train Loss = {train_loss/len(train_dataloader):.4f}, "
              f"Val AUC = {val_auc:.4f}, Val AP = {val_ap:.4f}")
        
        # 早停检查
        if val_auc > best_auc:
            best_auc = val_auc
            patience_counter = 0
        else:
            patience_counter += 1
            if patience_counter >= max_patience:
                print(f"早停在 epoch {epoch+1}")
                break
    
    return best_auc

def run_baseline_experiments():
    """运行所有基线实验"""
    print("开始 Phase 1: 基线实验")
    
    # 设置随机种子
    Seed_everything(42)
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 配置
    config = {
        'node_features': 1024 + 22 + 20,  # T5 + DSSP + BLOSUM62
        'edge_features': 32,
        'hidden_dim': 128,
        'num_encoder_layers': 3,  # 减少层数加快实验
        'k_neighbors': 30,
        'dropout': 0.2,
        'batch_size': 4,  # 小批量
        'epochs': 20,  # 减少epoch数
    }
    
    # 加载数据
    print("加载数据...")
    protein_data = {}  # 这里需要根据实际情况加载数据
    
    # 创建简单的数据分割（这里用虚拟数据演示）
    # 在实际使用中，需要加载真实的蛋白质数据
    print("注意：这是演示版本，使用虚拟数据")
    print("在实际使用中，请加载真实的蛋白质数据集")
    
    # 基线模型类型
    baseline_types = ['non_graph', 'geometry_only', 'topology_only']
    results = {}
    
    # 运行每个基线
    for model_type in baseline_types:
        print(f"\n开始测试 {model_type} 基线...")
        
        # 这里应该进行K-fold交叉验证
        # 为了演示，我们只运行一个fold
        fold_results = []
        
        # 创建虚拟的训练和验证数据
        # 在实际使用中，这里应该是真实的数据分割
        dummy_df = pd.DataFrame({'pdb_id': [f'protein_{i}' for i in range(10)]})
        train_df = dummy_df[:8]
        val_df = dummy_df[8:]
        
        try:
            auc = train_baseline_model(model_type, train_df, val_df, config, 1, device)
            fold_results.append(auc)
            print(f"{model_type} 基线 AUC: {auc:.4f}")
        except Exception as e:
            print(f"训练 {model_type} 时出错: {str(e)}")
            fold_results.append(0.0)
        
        results[model_type] = {
            'mean_auc': np.mean(fold_results),
            'std_auc': np.std(fold_results),
            'fold_results': fold_results
        }
    
    # 输出结果
    print("\n" + "="*60)
    print("Phase 1 基线实验结果:")
    print("="*60)
    for model_type, result in results.items():
        print(f"{model_type:15s}: AUC = {result['mean_auc']:.4f} ± {result['std_auc']:.4f}")
    
    return results

if __name__ == "__main__":
    results = run_baseline_experiments()
